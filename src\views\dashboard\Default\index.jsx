import { useEffect, useState } from 'react';

// material-ui
import Grid from '@mui/material/Grid2';

// project imports
import TotalProductsCard from './TotalProductsCard';
import TotalOrdersCard from './TotalOrdersCard';
import TotalClientsCard from './TotalClientsCard';
import TotalRevenueCard from './TotalRevenueCard';
import RecentOrdersCard from './RecentOrdersCard';
import SalesGrowthChart from './SalesGrowthChart';

import { gridSpacing } from 'store/constant';

// services
import { fetchDashboardMetrics } from '../../../services/dashboardService';

// ==============================|| DEFAULT DASHBOARD ||============================== //

export default function Dashboard() {
  const [isLoading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState({
    totalProducts: 0,
    totalOrders: 0,
    totalClients: 0,
    totalRevenue: 0,
    recentOrders: [],
    salesData: []
  });
  const [error, setError] = useState(null);

  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        const data = await fetchDashboardMetrics();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        console.error('Error loading dashboard data:', err);
        setError('Erreur lors du chargement des données du tableau de bord');
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  return (
    <Grid container spacing={gridSpacing}>
      <Grid size={12}>
        <Grid container spacing={gridSpacing}>
          <Grid size={{ lg: 3, md: 6, sm: 6, xs: 12 }}>
            <TotalProductsCard isLoading={isLoading} total={dashboardData.totalProducts} error={error} />
          </Grid>
          <Grid size={{ lg: 3, md: 6, sm: 6, xs: 12 }}>
            <TotalOrdersCard isLoading={isLoading} total={dashboardData.totalOrders} error={error} />
          </Grid>
          <Grid size={{ lg: 3, md: 6, sm: 6, xs: 12 }}>
            <TotalClientsCard isLoading={isLoading} total={dashboardData.totalClients} error={error} />
          </Grid>
          <Grid size={{ lg: 3, md: 6, sm: 6, xs: 12 }}>
            <TotalRevenueCard isLoading={isLoading} total={dashboardData.totalRevenue} error={error} />
          </Grid>
        </Grid>
      </Grid>
      <Grid size={12}>
        <Grid container spacing={gridSpacing}>
          <Grid size={{ xs: 12, md: 8 }}>
            <SalesGrowthChart isLoading={isLoading} data={dashboardData.salesData} error={error} />
          </Grid>
          <Grid size={{ xs: 12, md: 4 }}>
            <RecentOrdersCard isLoading={isLoading} orders={dashboardData.recentOrders} error={error} />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
}
